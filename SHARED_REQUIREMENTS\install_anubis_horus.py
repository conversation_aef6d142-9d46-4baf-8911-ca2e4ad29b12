#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 مثبت تلقائي لأنظمة أنوبيس وحورس
Automatic Installer for Anubis and Horus Systems

يقوم بتثبيت جميع المتطلبات اللازمة لتشغيل أنظمة أنوبيس وحورس
Installs all necessary requirements to run Anubis and Horus systems
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
import time

class AnubisHorusInstaller:
    def __init__(self):
        self.system = platform.system()
        self.python_version = sys.version_info
        self.base_dir = Path(__file__).parent
        self.requirements_file = self.base_dir / "requirements_anubis_horus_unified.txt"
        
        print("🚀 مثبت أنظمة أنوبيس وحورس")
        print("=" * 60)
        print(f"🖥️ النظام: {self.system}")
        print(f"🐍 Python: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"📁 المجلد: {self.base_dir}")
        print("=" * 60)

    def check_python_version(self):
        """التحقق من إصدار Python"""
        print("🔍 التحقق من إصدار Python...")
        
        if self.python_version < (3, 8):
            print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
            print(f"   الإصدار الحالي: {self.python_version.major}.{self.python_version.minor}")
            return False
        
        if self.python_version < (3, 11):
            print("⚠️ تحذير: Python 3.11+ مفضل للأداء الأمثل")
        
        print(f"✅ إصدار Python مناسب: {self.python_version.major}.{self.python_version.minor}")
        return True

    def check_pip(self):
        """التحقق من pip وتحديثه"""
        print("📦 التحقق من pip...")
        
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ pip متوفر: {result.stdout.strip()}")
            else:
                print("❌ pip غير متوفر")
                return False
        except Exception as e:
            print(f"❌ خطأ في التحقق من pip: {e}")
            return False
        
        # تحديث pip
        print("⬆️ تحديث pip...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            print("✅ تم تحديث pip")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ تحذير: لم يتم تحديث pip: {e}")
        
        return True

    def create_virtual_environment(self):
        """إنشاء بيئة افتراضية"""
        venv_path = self.base_dir.parent / "anubis_horus_env"
        
        if venv_path.exists():
            print(f"📁 البيئة الافتراضية موجودة: {venv_path}")
            response = input("هل تريد إعادة إنشائها؟ (y/N): ").lower()
            if response == 'y':
                print("🗑️ حذف البيئة القديمة...")
                import shutil
                shutil.rmtree(venv_path)
            else:
                print("⏭️ تخطي إنشاء البيئة الافتراضية")
                return str(venv_path)
        
        print("🔨 إنشاء بيئة افتراضية...")
        try:
            subprocess.run([sys.executable, "-m", "venv", str(venv_path)], 
                         check=True, capture_output=True)
            print(f"✅ تم إنشاء البيئة الافتراضية: {venv_path}")
            
            # تعليمات التفعيل
            if self.system == "Windows":
                activate_cmd = f"{venv_path}\\Scripts\\activate"
            else:
                activate_cmd = f"source {venv_path}/bin/activate"
            
            print(f"💡 لتفعيل البيئة الافتراضية:")
            print(f"   {activate_cmd}")
            
            return str(venv_path)
            
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في إنشاء البيئة الافتراضية: {e}")
            return None

    def install_requirements(self):
        """تثبيت المتطلبات"""
        if not self.requirements_file.exists():
            print(f"❌ ملف المتطلبات غير موجود: {self.requirements_file}")
            return False
        
        print("📦 بدء تثبيت المتطلبات...")
        print(f"📄 ملف المتطلبات: {self.requirements_file}")
        
        # قراءة عدد المكتبات
        with open(self.requirements_file, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f.readlines() 
                    if line.strip() and not line.startswith('#')]
        
        print(f"📊 عدد المكتبات للتثبيت: {len(lines)}")
        print("⏱️ هذا قد يستغرق 15-30 دقيقة...")
        
        start_time = time.time()
        
        try:
            # تثبيت المتطلبات الأساسية أولاً
            basic_packages = ["wheel", "setuptools"]
            print("🔧 تثبيت المكتبات الأساسية...")
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade"] + basic_packages,
                         check=True, capture_output=True)
            
            # تثبيت جميع المتطلبات
            print("📦 تثبيت جميع المتطلبات...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "-r", str(self.requirements_file),
                "--upgrade"
            ], capture_output=True, text=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت جميع المتطلبات بنجاح!")
                print(f"⏱️ وقت التثبيت: {duration:.1f} ثانية")
                return True
            else:
                print(f"❌ خطأ في التثبيت:")
                print(result.stderr)
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في تثبيت المتطلبات: {e}")
            return False

    def verify_installation(self):
        """التحقق من التثبيت"""
        print("🔍 التحقق من التثبيت...")
        
        # قائمة المكتبات الأساسية للاختبار
        test_packages = [
            "fastapi",
            "uvicorn", 
            "torch",
            "transformers",
            "langchain",
            "sqlalchemy",
            "pydantic",
            "numpy",
            "pandas"
        ]
        
        failed_packages = []
        
        for package in test_packages:
            try:
                __import__(package)
                print(f"   ✅ {package}")
            except ImportError:
                print(f"   ❌ {package}")
                failed_packages.append(package)
        
        if failed_packages:
            print(f"\n⚠️ فشل في تثبيت {len(failed_packages)} مكتبة:")
            for package in failed_packages:
                print(f"   - {package}")
            return False
        else:
            print("\n🎉 تم التحقق من جميع المكتبات الأساسية بنجاح!")
            return True

    def show_next_steps(self):
        """عرض الخطوات التالية"""
        print("\n" + "=" * 60)
        print("🎯 الخطوات التالية:")
        print("=" * 60)
        
        print("1️⃣ تشغيل نظام أنوبيس:")
        print("   cd ANUBIS_SYSTEM")
        print("   python main.py")
        print("   🌐 ثم افتح: http://localhost:8000")
        
        print("\n2️⃣ اختبار فريق حورس:")
        print("   python -c \"from HORUS_AI_TEAM.horus_interface import horus; print(horus.ask('مرحبا'))\"")
        
        print("\n3️⃣ تشغيل الاختبارات:")
        print("   python -m pytest ANUBIS_SYSTEM/tests/")
        
        print("\n4️⃣ فتح Jupyter Lab:")
        print("   jupyter lab")
        
        print("\n📚 للمزيد من المعلومات:")
        print("   - اقرأ SHARED_REQUIREMENTS/README.md")
        print("   - راجع SHARED_REQUIREMENTS/ANUBIS_HORUS_REQUIREMENTS_ANALYSIS.md")
        
        print("\n🎉 أنظمة أنوبيس وحورس جاهزة للاستخدام!")

    def run_installation(self):
        """تشغيل عملية التثبيت الكاملة"""
        print("🚀 بدء عملية التثبيت...")
        
        # التحقق من المتطلبات الأساسية
        if not self.check_python_version():
            return False
        
        if not self.check_pip():
            return False
        
        # سؤال المستخدم عن البيئة الافتراضية
        print("\n" + "=" * 60)
        create_venv = input("هل تريد إنشاء بيئة افتراضية؟ (Y/n): ").lower()
        if create_venv != 'n':
            venv_path = self.create_virtual_environment()
            if venv_path:
                print(f"\n💡 تذكر تفعيل البيئة الافتراضية قبل الاستخدام!")
        
        # تثبيت المتطلبات
        print("\n" + "=" * 60)
        if not self.install_requirements():
            print("❌ فشل في تثبيت المتطلبات")
            return False
        
        # التحقق من التثبيت
        print("\n" + "=" * 60)
        if not self.verify_installation():
            print("⚠️ بعض المكتبات لم يتم تثبيتها بشكل صحيح")
            print("💡 جرب تشغيل: pip install -r requirements_anubis_horus_unified.txt")
        
        # عرض الخطوات التالية
        self.show_next_steps()
        
        return True

def main():
    """الدالة الرئيسية"""
    installer = AnubisHorusInstaller()
    
    try:
        success = installer.run_installation()
        if success:
            print("\n🎉 تم إكمال التثبيت بنجاح!")
        else:
            print("\n❌ فشل في التثبيت")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف التثبيت بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
